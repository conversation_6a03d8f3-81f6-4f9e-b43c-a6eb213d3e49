import { join } from "path";
import { readFileSync } from "fs";
import express from "express";
import serveStatic from "serve-static";
import shopify from "./shopify.js";
import GDPRWebhookHandlers from "./gdpr.js";
import * as dotenv from "dotenv";
dotenv.config();
import cors from 'cors';
import EditCartRoute from "./backend/Routes/handleCartControlRoute.js";
import verifyWebhook from "./middleware/verifyWebhook.js";
import checkStoreCompatibility from "./checkEligibility.js";
import {
  createAppRecurringSubscription,
  checkSubscription,
  cancelAppSubscription
} from "./billing.js";
const PORT = process.env.PORT;
const STATIC_PATH =
  process.env.NODE_ENV === "production"
    ? `${process.cwd()}/frontend/dist`
    : `${process.cwd()}/frontend/`;

const app = express();
app.use(cors())
// Set up Shopify authentication and webhook handling
app.get(shopify.config.auth.path, shopify.auth.begin());
app.get(
  shopify.config.auth.callbackPath,
  shopify.auth.callback(),
  shopify.redirectToShopifyOrAppRoot()
);
app.post(
  shopify.config.webhooks.path,
  shopify.processWebhooks({ webhookHandlers: GDPRWebhookHandlers })
);

// If you are adding routes outside of the /api path, remember to
// also add a proxy rule for them in web/frontend/vite.config.js
app.use(express.json());


app.get("/test", (req, res) => {
  res.send("OK");
});


app.post("/customer/request", verifyWebhook, (req, res) => {
  res.status(200).send("OK");
});
app.post("/customer/erasure", verifyWebhook, (req, res) => {
  res.status(200).send("OK");
});
app.post("/shop/erasure", verifyWebhook, (req, res) => {
  res.status(200).send("OK");
});

app.get("/api/test",(req,res)=>res.send("workingg"));

app.use("/api/*", shopify.validateAuthenticatedSession());
app.use(shopify.cspHeaders());
app.use(serveStatic(STATIC_PATH, { index: false }));
app.use('/api/metaUpdate',EditCartRoute);
app.post("/api/checkEligibility", checkStoreCompatibility);
app.get("/api/checkSubscription", async (req, res) => {
  const data = await checkSubscription(res.locals.shopify.session);
  res.send(data);
});
app.get("/api/cancelSubscription/:id", async (_req, res) => {
  let status = 200;
  let error = null;
  let data = null;
  try {
    data = await cancelAppSubscription(
      res.locals.shopify.session,
      _req.params.id
    );
  } catch (e) {
    console.log(`Failed to cancel app billing: ${e.message}`);
    status = 500;
    error = e.message;
  }
  res.status(status).send({ success: status === 200, error, data });
});
app.get("/api/createAppSubscription", async (_req, res) => {
  let status = 200;
  let error = null;
  let data = null;
  try {
    data = await createAppRecurringSubscription(res.locals.shopify.session);
  } catch (e) {
    console.log(`Failed to process app billing: ${e.message}`);
    status = 500;
    error = e.message;
  }
  res.status(status).send({ success: status === 200, error, data });
});
app.use("/*", shopify.ensureInstalledOnShop(), async (_req, res, _next) => {
  return res
    .status(200)
    .set("Content-Type", "text/html")
    .send(readFileSync(join(STATIC_PATH, "index.html")));
});

app.listen(PORT);
