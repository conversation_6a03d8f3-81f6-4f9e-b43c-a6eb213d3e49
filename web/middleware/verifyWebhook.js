import crypto from "crypto";
export default function verifyWebhook(req, res, next) {
  console.log("Verify webhook invoked");
  let hmac;
  let data;
  try {
    hmac = req.headers["x-shopify-hmac-sha256"];
    data = req.body;
  } catch (e) {
    console.log(
      `Webhook request failed from: ${req.get("X-Shopify-Shop-Domain")}`
    );
    return res.status(401).send({ error: "unauthorized" });
  }
  if (!hmac) {
    console.log("hmac undefined");
    return res.status(401).send({ error: "unauthorized" });
  } else if (!data || typeof data !== "object") {
    console.log("data undefined");
    return res.status(401).send({ error: "unauthorized" });
  }
  const sharedSecret = process.env.SHOPIFY_API_SECRET;
  const calculatedSignature = crypto
    .createHmac("sha256", sharedSecret)
    .update(Buffer.from(JSON.stringify(data)), "utf8")
    .digest("base64");
  if (calculatedSignature === req.headers["x-shopify-hmac-sha256"]) {
    console.log("Webhook Verified");
    return next();
  } else {
    console.log("Webhook Not Verified");
    return res.status(401).send({ error: "unauthorized" });
  }
}
