<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />

    <!-- Ensures that the UI is properly scaled in the Shopify Mobile app -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <script type="module">
      if (!import.meta.env || !import.meta.env.PROD) {
        // Because the base HTML is rendered in the server side, we need to manually embed the code to enable HMR in our
        // code, so that the vite server is properly enabled to run HMR
        const script = document.createElement("script");
        script.setAttribute("type", "module");
        script.setAttribute("src", "./dev_embed.js");
        document.getElementsByTagName("head")[0].append(script);
      }
    </script>
    <!--Start of Tawk.to Script-->
    <script type="text/javascript">
      var Tawk_API = Tawk_API || {},
        Tawk_LoadStart = new Date();
      (function () {
        var s1 = document.createElement("script"),
          s0 = document.getElementsByTagName("script")[0];
        s1.async = true;
        s1.src = "https://embed.tawk.to/654b60f5a84dd54dc489d7b3/1hen75g17";
        s1.charset = "UTF-8";
        s1.setAttribute("crossorigin", "*");
        s0.parentNode.insertBefore(s1, s0);
      })();
    </script>
    <!--End of Tawk.to Script-->
  </head>
  <body>
    <div id="app"><!--index.jsx injects App.jsx here--></div>
    <script type="module" src="/index.jsx"></script>
  </body>
</html>
