{"HomePage": {"heading": "Nice work on building a Shopify app 🎉", "learnMore": "Learn more about building out your app in <ShopifyTutorialLink>this Shopify tutorial</ShopifyTutorialLink> 📚", "startPopulatingYourApp": "Ready to go? Start populating your app with some sample products to view and test in your store.", "title": "App name", "trophyAltText": "Nice work on building a Shopify app", "yourAppIsReadyToExplore": "Your app is ready to explore! It contains everything you need to get started including the <PolarisLink>Polaris design system</PolarisLink>, <AdminApiLink>Shopify Admin API</AdminApiLink>, and <AppBridgeLink>App Bridge</AppBridgeLink> UI library and components."}, "NavigationMenu": {"pageName": "Page name"}, "NotFound": {"description": "Check the URL and try again, or use the search bar to find what you need.", "heading": "There is no page at this address"}, "PageName": {"body": "Body", "heading": "Heading", "primaryAction": "Primary action", "secondaryAction": "Secondary action", "title": "Page name"}, "ProductsCard": {"description": "Sample products are created with a default title and price. You can remove them at any time.", "errorCreatingProductsToast": "There was an error creating products", "populateProductsButton": {"one": "Populate {{count}} product", "other": "Populate {{count}} products"}, "productsCreatedToast": {"one": "{{count}} product created!", "other": "{{count}} products created!"}, "title": "Product Counter", "totalProductsHeading": "TOTAL PRODUCTS"}}