{"HomePage": {"heading": "Gute Arbeit beim Erstellen einer Shopify-App 🎉", "learnMore": "Erhalte mehr Informationen zum Ausbau deiner App in <ShopifyTutorialLink>diesem Shopify-Tutorial</ShopifyTutorialLink> 📚", "startPopulatingYourApp": "Bist du bereit? <PERSON>elle in deiner App einige Beispielprodukte bereit, um sie in deinem Shop anzuzeigen und zu testen.", "title": "App-Name", "trophyAltText": "Gute Arbeit beim Erstellen einer Shopify-App", "yourAppIsReadyToExplore": "Deine App kann nun erkundet werden! Sie enthält alles, was du brauchst, um loszulegen: das <PolarisLink>Polaris Designsystem</PolarisLink>, die <AdminApiLink>Shopify Admin API</AdminApiLink> und die <AppBridgeLink>App Bridge</AppBridgeLink>-UI-Bibliothek sowie entsprechende Komponenten."}, "NavigationMenu": {"pageName": "<PERSON><PERSON><PERSON><PERSON>"}, "NotFound": {"description": "Überprüfe die URL und versuche es erneut. Alternativ kannst du die Suchleiste verwenden, um zu finden, was du suchst.", "heading": "Unter dieser Adresse gibt es keine Seite"}, "PageName": {"body": "Nachricht", "heading": "Überschrift", "primaryAction": "Primäre Aktion", "secondaryAction": "Sekundäre Aktion", "title": "<PERSON><PERSON><PERSON><PERSON>"}, "ProductsCard": {"description": "Beispielprodukte werden mit einem Standardtitel und -preis erstellt. Du kannst sie jederzeit entfernen.", "errorCreatingProductsToast": "<PERSON><PERSON> von Produkten ist ein Fehler aufgetreten", "populateProductsButton": {"one": "{{count}} Produkt bereitstellen", "other": "{{count}} Produkte bereitstellen"}, "productsCreatedToast": {"one": "{{count}} Produkt erstellt!", "other": "{{count}} Produkte erstellt!"}, "title": "Produ<PERSON><PERSON><PERSON><PERSON>", "totalProductsHeading": "PRODUKTE INSGESAMT"}}