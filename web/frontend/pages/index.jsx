import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Legacy<PERSON><PERSON>ck,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Spinner,
  Layout
} from "@shopify/polaris";
import React, { useEffect, useState, useCallback } from "react";
import { ResourcePicker } from "@shopify/app-bridge-react";
import Switch from "react-ios-switch";
import CartControlEnable from "../components/CartControlEnable"
import GiftWrapOptionEnableImg from "../components/GiftMessageOptionEnabled";
import BillingWarningBanner from "../components/BillingWarning";
import { useAuthenticatedFetch } from "../hooks";
import EligibilityModal from '../components/EligibilityModal';
import FAQModal from '../components/FAQModal';

const CartHome = () => {
  const fetch = useAuthenticatedFetch();
  const [billingObject, setBillingObject] = useState({});
  const [orderCount, setOrderCount] = useState(0);
  const [loadingLoader, setLoadingLoader] = useState(false);
  const [btnLoading, setBtnLoading] = useState();
  const [active, setActive] = useState(false);
  const [isCartControlEnabled, setIsCartControlEnabled] = useState(false);
  const [isGiftWrapOptionEnabled, setGiftWrapOptionEnabled] = useState(false);
  const [isGiftMessageOptionEnabled, setGiftMessageOptionEnabled] = useState(false);
  const [productTags, setProductTags] = useState("");
  const [productVariantID, setProductVariantID] = useState("");
  const [showResourcePicker, setShowResourcePicker] = useState(false);
  const [variantError,setVariantError] = useState();

  const toggleActive = useCallback(() => setActive((active) => !active), []);

  const toastMarkup = active ? (
    <Toast content="Successfully Saved" onDismiss={toggleActive} />
  ) : null;

  const toggleBtnLoading = (value) => {
    setBtnLoading(value);
  };
  const handleShowResourcePicker = () => setShowResourcePicker(!showResourcePicker);
  const handleSelectedResourcePicker = (product) => {
    let productVariants = product.selection[0].variants;
    productVariants.length > 1 ? setVariantError(true) : setVariantError(false);
    if(!variantError){
      setProductVariantID(productVariants[0].id.split("/")[4]);
    }
  }
  const handleSwitchChange = () => {
    setIsCartControlEnabled(!isCartControlEnabled);
    setProductTags("");
  };
  const handleGiftWrapSwitch = () => {
    setGiftWrapOptionEnabled(!isGiftWrapOptionEnabled);
    setProductVariantID("");
  }
  const handleGiftMessageSwitch = () => {
    setGiftMessageOptionEnabled(!isGiftMessageOptionEnabled);
  }
  const handleProductTagsField = (value) => {
    setProductTags(value);
  }
  const handleProductVariantIDField = (value) => {
    setProductVariantID(value);
  }
  const handleSaveClick = () => {
    if (isCartControlEnabled) {
      toggleBtnLoading(true);
      const CartObj = JSON.stringify({
        CC: isCartControlEnabled ? true : false,
        GW: isGiftWrapOptionEnabled ? true : false,
        GM: isGiftMessageOptionEnabled ? true : false,
        PT: productTags ? productTags : null,
        PV: productVariantID ? productVariantID : null
      });
      fetch(`/api/metaUpdate/editCart`, {
        method: "post",
        body: CartObj,
        headers: {
          "Content-Type": "application/json",
        },
      })
        .then((result) => result.text())
        .then((response) => {
          console.log(response);
          { toggleActive() }
          toggleBtnLoading(false);
        })
        .catch((error) => console.log("error", error));
    }
  };
  // const handleSaveDisabled = () => {
  //   if (orderCount !== 15) {
  //     if (isCartControlEnabled && isGiftWrapOptionEnabled) {
  //       if (productTags.trim() !== "" && productVariantID.trim() !== "") {
  //         return true;
  //       } else {
  //         return false;
  //       }
  //     } else if (isCartControlEnabled) {
  //       if (productTags.trim() !== "") {
  //         return true;
  //       } else {
  //         return false;
  //       }
  //     } else if (isGiftWrapOptionEnabled) {
  //       if (productVariantID.trim() !== "") {
  //         return true;
  //       } else {
  //         return false;
  //       }
  //     }
  //   }
  // }
  async function checkBillingStatus() {
    const response = await fetch("/api/checkSubscription");
    if (response.ok) {
      const data = await response.json();
      if (data.developmentStore || data.subscriptionStatus) {
        setOrderCount(data.orderCount);
      }
      setBillingObject(data);
    } else {
      console.error(`Request failed with status ${response.status}`);
    }
  }
  const getSavedMeta = async () => {
    fetch("/api/metaUpdate/savedMeta")
      .then((result) => result.json())
      .then((response) => {
        console.log(response)
        if (response) {
          let value = JSON.parse(response?.value)
          value.CC ? setIsCartControlEnabled(true) : setIsCartControlEnabled(false);
          value.PT ? setProductTags(value.PT) : setProductTags("");
          value.GW ? setGiftWrapOptionEnabled(true) : setGiftWrapOptionEnabled(false);
          value.PV ? setProductVariantID(value.PV) : setProductVariantID("");
          value.GM ? setGiftMessageOptionEnabled(true) : setGiftMessageOptionEnabled(false);
        }
        setLoadingLoader(true);
      })
      .catch((error) => console.log("error", error));
  };
  useEffect(() => {
    async function fetchData() {
      await checkBillingStatus();
      await getSavedMeta();
    }
    fetchData();
  }, []);
  const pillCSS = {
    display: "inline-block",
    backgroundColor: "#F5F5F5",
    color: "black",
    padding: "0px 10px",
    border: "1px solid black",
    borderRadius: "50px",
    fontSize: "10px",
  };

  return (
    <Frame>
      {!loadingLoader && (
        <Layout.Section>
          <LegacyCard.Section>
            <LegacyStack alignment="center" distribution="center">
              <Spinner accessibilityLabel="Spinner" size="large" />
            </LegacyStack>
          </LegacyCard.Section>
        </Layout.Section>
      )}
      {loadingLoader && (
        <Page>
          <Layout>
            <Layout.Section>
              <LegacyCard title="Edit Cart on Checkout">
                <LegacyCard.Section>
                  <Text variant="bodyMd">
                    Cart Control simplifies checkout for customers, letting them adjust items easily. Merchants can streamline operations by specifying variant IDs to exclude certain products from modification options. Additionally, customers can add a gift wrap during checkout for that extra touch.
                  </Text>
                </LegacyCard.Section>
              </LegacyCard>
              <LegacyCard>
                <BillingWarningBanner billingObject={billingObject} />
              </LegacyCard>
              <LegacyCard.Section>
                <LegacyStack alignment="center" distribution="leading">
                  <Switch
                    style={{
                      background: isCartControlEnabled ? "cartControlEnabled" : "grey",
                    }}
                    checked={isCartControlEnabled}
                    onChange={handleSwitchChange}
                  />
                  <Text>Allow Cart Control on Checkout</Text>
                  <div style={pillCSS}>
                    <span>{isCartControlEnabled ? "Active" : "Inactive"}</span>
                  </div>
                </LegacyStack>
              </LegacyCard.Section>

              <LegacyCard>
                <LegacyCard.Section>
                  <CartControlEnable isCartControlEnabled={isCartControlEnabled} />
                </LegacyCard.Section>

                <LegacyCard.Section>
                  <TextField
                    label="Enter the tags for the products where we shouldn't include the modify option"
                    value={productTags}
                    placeholder="Enter Product Tags"
                    onChange={handleProductTagsField}
                    disabled={!isCartControlEnabled}
                    helpText="Eg: Freebies or complimentary items, limited-stock items, personalized/customized goods, and promotional bundles"
                  />
                </LegacyCard.Section>
              </LegacyCard>

              <LegacyCard.Section>
                <LegacyStack alignment="center" distribution="leading">
                  <Switch
                    style={{
                      background: isGiftWrapOptionEnabled ? "GiftWrapOptionEnabled" : "grey",
                    }}
                    checked={isGiftWrapOptionEnabled}
                    onChange={handleGiftWrapSwitch}
                  />
                  <Text>Add a Gift Wrap Option</Text>
                  <div style={pillCSS}>
                    <span>{isGiftWrapOptionEnabled ? "Active" : "Inactive"}</span>
                  </div>
                </LegacyStack>
              </LegacyCard.Section>


              <LegacyCard>
                <LegacyCard.Section>
                  <Text variant="bodyMd">
                    Steps to to Add Gift Wrap at Checkout:
                  </Text>
                  <br></br>
                  <List type="number">
                    <List.Item>
                      Make a Gift Wrap product in the admin panel.
                    </List.Item>
                    <List.Item>
                      Give it a title, like "Gift Wrap."
                    </List.Item>
                    <List.Item>
                      Attach a price and an image to the Gift Wrap product.
                    </List.Item>
                    <List.Item>
                      After creating the product successfully, include the product variant ID below.
                    </List.Item>
                    <List.Item>
                      This entered product variant ID will now serve as the gift wrap option during checkout.
                    </List.Item>
                  </List>
                </LegacyCard.Section>

                <LegacyCard.Section>
                  <div style={{ width: "100%", display: "flex", justifyContent: 'space-around'}}>
                    <div style={{ width: '78%' }}>
                      <TextField
                        label="Enter the variant ID of the Gift Wrap Product"
                        value={productVariantID}
                        placeholder="Enter Product Variant Id"
                        onChange={handleProductVariantIDField}
                        disabled={!isGiftWrapOptionEnabled}
                        error={variantError? "Please Select only one variant of product":""}
                        helpText="Note: Above entered product title, price, and image will be shown at checkout with add and remove options."
                      />
                    </div>
                    <div style={{ width: '18%',marginTop:"24px"}}>
                      <Button disabled={!isGiftWrapOptionEnabled} primary fullWidth onClick={handleShowResourcePicker}>Browse</Button>
                    </div>
                    <ResourcePicker
                      resourceType="Product"
                      selectMultiple={false}
                      showVariants={true}
                      open={showResourcePicker}
                      onSelection={(selected) => handleSelectedResourcePicker(selected)}
                      onCancel={handleShowResourcePicker}
                    />

                  </div>

                </LegacyCard.Section>

                <LegacyCard.Section>
                  <LegacyStack alignment="center" distribution="leading">
                    <Switch
                      style={{
                        background: isGiftMessageOptionEnabled ? "GiftMessageOptionEnabled" : "grey",
                      }}
                      checked={isGiftMessageOptionEnabled}
                      onChange={handleGiftMessageSwitch}
                    />
                    <Text>Add a Gift Message Option</Text>
                    <div style={pillCSS}>
                      <span>{isGiftMessageOptionEnabled ? "Active" : "Inactive"}</span>
                    </div>
                  </LegacyStack>
                </LegacyCard.Section>
                <GiftWrapOptionEnableImg isGiftWrapOptionEnabled={isGiftWrapOptionEnabled} isGiftMessageOptionEnabled={isGiftMessageOptionEnabled} />
              </LegacyCard>
            </Layout.Section>
            <Layout.Section secondary>
              <LegacyCard title="Cart Editr setup instructions">
                <LegacyCard.Section>
                  <Text>
                    Steps to enable Cart Editr extension at checkout
                  </Text>
                  <br></br>
                  <List type="bullet">
                    <List.Item>
                      {
                        'Access the admin panel and navigate to "Settings" > "Checkout" > "Checkout Customization" > "Customize."'
                      }
                    </List.Item>
                    <List.Item>
                      In the Checkout Editor, choose to add the app block from the section-block panel and enable the "Cart Editr" as required.
                    </List.Item>
                    <List.Item>
                      Select the Cart Editr block to enable the Edit Cart functionality.
                    </List.Item>
                    <List.Item>
                      Save your changes to enable the functionality on the checkout page.
                    </List.Item>

                  </List>
                </LegacyCard.Section>
                <LegacyCard.Section>
                  <Button primary>
                    <a
                      style={{ textDecoration: "none", color: "white" }}
                      href={`https://admin.shopify.com/store/${window.location.href.split("&shop=")[1].split(".myshopify.com")[0]}/settings/checkout/editor?page=information&extensionPicker=true`}
                      target="_blank"
                    >
                      Setup in your theme
                    </a>
                  </Button>
                </LegacyCard.Section>
              </LegacyCard>
            </Layout.Section>
          </Layout>
          <LegacyCard.Section>
            <LegacyStack alignment="center">
              <Button primary loading={btnLoading} onClick={handleSaveClick} 
              // disabled={!handleSaveDisabled()}
              >
                Save
              </Button>
            </LegacyStack>
          </LegacyCard.Section>
          <FAQModal />
          {toastMarkup}
        </Page>
      )}
      <EligibilityModal />
    </Frame>
  );
};

export default CartHome;
