import React from 'react'
import { Text, LegacyStack, LegacyCard } from "@shopify/polaris";

const GiftWrapOptionEnableImg = ({ isGiftWrapOptionEnabled, isGiftMessageOptionEnabled }) => {
    return (
        <LegacyCard.Section>
            <LegacyStack distribution="center"> <Text variant="headingLg" as="strong">Checkout Preview</Text></LegacyStack>
            <div style={{ width: "100%", margin: 'auto', marginTop: '4%' }}>
                <div style={{ width: "70%", margin: 'auto', display: isGiftWrapOptionEnabled ? isGiftMessageOptionEnabled ? "none" : "block" : "none" }}>
                    <img alt="" width="100%" height="auto"
                        src="https://stc-apps.s3.us-west-2.amazonaws.com/Image-1.jpg"
                    />
                </div>
                <div style={{ width: "70%", margin: 'auto', display: isGiftWrapOptionEnabled ? "none" : isGiftMessageOptionEnabled ? "none" : "block" }}>
                    <img alt="" width="100%" height="auto"
                        src="https://stc-apps.s3.us-west-2.amazonaws.com/Image-2.jpg"
                    />
                </div>
                <div style={{ width: "70%", margin: 'auto', display: isGiftWrapOptionEnabled ? isGiftMessageOptionEnabled ? "block" : "none" : "none" }}>
                    <img alt="" width="100%" height="auto"
                        src="https://stc-apps.s3.us-west-2.amazonaws.com/image-3.jpg"
                    />
                </div>
            </div>
        </LegacyCard.Section>
    )
}


export default GiftWrapOptionEnableImg