import React from 'react'
import {
  LegacyCard,
  Text,
  LegacyStack,
} from "@shopify/polaris";

const CartControlEnable = ({ isCartControlEnabled }) => {
  return (
    <>
      <LegacyStack distribution="center"> <Text variant="headingLg" as="strong">Checkout Preview</Text></LegacyStack>
      <div style={{ width: "100%", margin: 'auto', marginTop: '4%' }}>
        <div style={{ width: "70%", margin: 'auto', display: isCartControlEnabled ? "block" : "none" }}>
          <img alt="" width="100%" height="auto" src="https://lakshaybucketstc.s3.us-west-2.amazonaws.com/Image-2.jpg" />
        </div>
        <div style={{ width: "70%", margin: 'auto', display: isCartControlEnabled ? "none" : "block" }}>
          <img alt="" width="100%" height="auto" src="https://lakshaybucketstc.s3.us-west-2.amazonaws.com/Image-1.jpg" />
        </div>

      </div>
    </>
  )
}

export default CartControlEnable;