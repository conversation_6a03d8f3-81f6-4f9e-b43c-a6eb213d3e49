import React, { useState, useCallback } from 'react'
import { LegacyCard, LegacyStack, Button, Modal, Collapsible, Text,Link } from '@shopify/polaris'

const FAQModal = () => {
    const [active, setActive] = useState(false);
    const handleModal = useCallback(() => setActive(!active), [active]);
  
    const activator = (
      <div style={{ marginTop: "8%" }}>
        <LegacyStack distribution="center">
        <Link url="https://stc-apps.s3.us-west-2.amazonaws.com/Edit+Cart+on+Checkout+-++How+to+Use.pdf">
            How to use discount ?
          </Link>
        </LegacyStack>
        <br></br>
        <LegacyStack distribution="center">
          <Button onClick={handleModal}>FAQ?</Button>
        </LegacyStack>
      </div>
    );
    const [openFaq, setOpenFaq] = useState(Array(10).fill(false));
  
    const faqClick = (num) => {
      const updatedFaq = [...openFaq];
      updatedFaq[num - 1] = !updatedFaq[num - 1];
      setOpenFaq(updatedFaq);
    };
  
    const faqData = [
      {
        question:
          "How does the app enhance checkout for Shopify Plus stores?",
        answer:
          "The app enables advanced cart edits, allowing customers to modify quantities, remove items, and change variants during checkout.",
      },
      {
        question: `How can I exclude certain products from modification functionality?`,
        answer: `Simply add tags to products that shouldn't be modified (e.g., freebies, limited-stock items), and mention those tags in the app's input box.`,
      },
      {
        question:
          "Are personalized gift messages supported?",
        answer:
          "Yes, the app includes a gift message feature, allowing customers to leave personalized messages for their purchases.",
      },
      {
        question: "Does the app offer a gift wrap option?",
        answer:
          "Absolutely, enhance the customer experience by providing a gift wrap option for their orders.",
      },
      {
        question:
          "What types of products are typically excluded from modification?",
        answer: `Products like freebies, limited-stock items, personalized/customized goods, and promotional bundles are excluded from modification functionality for a seamless checkout experience.`,
      },
      {
        question:
          "Is the app compatible with all Shopify stores?",
        answer:
          "No, the app is specifically tailored for Shopify Plus stores with checkout extensibility enabled.",
      },
      {
        question:
          "Can customers modify subscription plans using the Edit Cart functionality?",
        answer:
          " Yes, customers can modify subscription plans if a subscription option is created for the product. The Edit Cart functionality seamlessly extends to subscription modifications, providing flexibility to customers in managing their subscriptions.",
      },
      // Add more FAQ items here
    ];

  return (
    <LegacyCard.Section>
          <LegacyStack
            vertical
            alignment="center"
            distribution="center"
            spacing="extraTight"
          >
            <Text>Developed by <Link url="https://seventhtriangle.com/">Seventh Triangle Consulting</Link></Text>
            <Text>
              Shopify Plus Partner providing expert UI/UX, theme customization,
              custom apps, and performance marketing
            </Text>
          </LegacyStack>
          <LegacyStack distribution="center">
            <Modal
              activator={activator}
              open={active}
              onClose={handleModal}
              title="Frequently Asked Questions"
            >
              <Modal.Section>
                {faqData.map((item, index) => (
                  <div key={index}>
                    <LegacyStack vertical>
                      <div
                        onClick={() => {
                          faqClick(index + 1);
                        }}
                        style={{
                          fontWeight: "bold",
                          cursor: "pointer",
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <div style={{ padding: "2% 0" }}>{item.question}</div>
                        <div>{openFaq[index] ? "▲" : "▼"}</div>
                      </div>
                      <Collapsible
                        open={openFaq[index]}
                        id={`faq-collapsible-${index}`}
                        transition={{
                          duration: "300ms",
                          timingFunction: "ease-in-out",
                        }}
                        expandOnPrint
                      >
                        <Text>{item.answer} </Text>
                      </Collapsible>
                    </LegacyStack>
                    {index < faqData.length - 1 && (
                      <hr style={{ marginTop: "1%" }} />
                    )}
                    {index === faqData.length - 1 && (
                      <div style={{ marginBottom: "1%" }} />
                    )}
                  </div>
                ))}
              </Modal.Section>
            </Modal>
          </LegacyStack>
        </LegacyCard.Section>
  )
}

export default FAQModal