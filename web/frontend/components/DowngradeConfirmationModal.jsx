import { Button, Modal } from "@shopify/polaris";
import { useState, useCallback } from "react";

function DowngradeConfirmationModal({
  disabled,
  loading,
  downgradePlan,
  subscriptionId,
}) {
  const [active, setActive] = useState(false);

  const toggleModal = useCallback(() => setActive((active) => !active), []);

  const activator = <Button onClick={toggleModal}>Downgrade Plan</Button>;

  return (
    <div>
      <Modal
        activator={activator}
        open={active}
        onClose={toggleModal}
        title="Downgrading App Plan"
        primaryAction={{
          content: "Cancel",
          onAction: toggleModal,
        }}
        secondaryActions={[
          {
            disabled: disabled,
            loading: loading,
            content: "Confirm",
            onAction: async () => {
              await downgradePlan(subscriptionId);
              toggleModal();
            },
          },
        ]}
      >
        <Modal.Section>
          Are you sure you want to downgrade your plan? Downgrading will limit
          you to only one discount at checkout.
        </Modal.Section>
      </Modal>
    </div>
  );
}

export default DowngradeConfirmationModal;