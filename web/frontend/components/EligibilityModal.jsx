import React, { useState, useEffect } from 'react'
import { Modal, Text } from '@shopify/polaris'

const EligibilityModal = () => {
    const [eligibleStore, setEligibleStore] = useState(true);
    const checkEligibility = async () => {
        await fetch("/api/checkEligibility", { method: "post" })
            .then((result) => result.text())
            .then((response) => {
                setEligibleStore(response);
            })
            .catch((error) => console.log("error", error));
    };
    useEffect(() => {
        checkEligibility();
    }, []);

    return (
        <>
            {!eligibleStore && (
                <Modal
                    open={true}
                    title="Important"
                    titleHidden
                >
                    <Modal.Section>
                        <Text variant="bodyMd">
                            Edit cart on checkout app functionality is exclusively available for
                            Shopify Plus stores with checkout extensibility enabled!
                        </Text>
                    </Modal.Section>
                </Modal>
            )}
        </>
    )
}

export default EligibilityModal