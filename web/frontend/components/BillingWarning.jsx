import { <PERSON>, <PERSON><PERSON>, List } from "@shopify/polaris";
import { useAuthenticatedFetch } from "../hooks";
import React, { useEffect, useState } from "react";
import WarningBanner from "./Warning";
import DowngradeConfirmationModal from "./DowngradeConfirmationModal";

export default function BillingWarningBanner({
  billingObject,
  checkBillingStatus,
  setLimit,
}) {
  const [loading, setLoading] = useState(false);
  const [content, setContent] = useState(null);
  const [error, setError] = useState(false);

  const fetch = useAuthenticatedFetch();

  async function redirectToBilling() {
    setLoading(true);
    try {
      const response = await fetch("/api/createAppSubscription");
      const data = await response.json();
      window.parent.location.href = data.data.confirmationUrl;
    } catch (error) {
      console.error(`Request failed with status ${error}`);
      setLoading(false);
    }
  }

  async function downgradePlan(id) {
    setLoading(true);
    try {
      const response = await fetch("/api/cancelSubscription/" + id);
      const res = await response.json();
      if ((res.data && res.data.error) || res.error) {
        setError(true);
      } else {
        setLimit(1);
        checkBillingStatus();
      }

      setLoading(false);
    } catch (error) {
      console.error(`Request failed with status ${error}`);
      setLoading(false);
    }
  }

  function setAppropriateTitle(billingObject) {
    if (billingObject.checkoutApiSupported) {
      if (billingObject.developmentStore) {
        const obj = {
          title:
            "You're using Standard Plan free as you're on development Store",
          desc: "All features will remain unlocked for free!",
          btn: false,
          tone: "info",
        };

        setContent(obj);
      } else if (billingObject.subscriptionStatus === false) {
        const obj = {
          title: "You're currently using the DEMO plan of the App.",
          desc: "Upgrade to Standard plan to unlock all features!",
          btn: true,
          tone: "warning",
        };
        setContent(obj);
      } else if (billingObject.subscriptionStatus === true) {
        const obj = {
          title: "You're currently using the Premium plan of the App.",
          desc: "You can downgrade anytime!",
          downgrade: true,
          subscriptionId: billingObject.subscriptionId,
          btn: true,
          tone: "warning",
        };
        setContent(obj);
      }
    } else if (billingObject.checkoutApiSupported === false) {
      const obj = {
        title:
          "You won't be able to use this app as Checkout extension is not yet enabled on your store.",
        desc: `Enable Checkout Extensibility: Upgrade Guide ${<Link url="https://help.shopify.com/en/manual/checkout-settings/checkout-extensibility/checkout-upgrade#upgrade-extensibility">(Shopify Guide)</Link>}`,
        btn: false,
        tone: "critical",
      };
      setContent(obj);
    }
  }

  useEffect(() => {
    setAppropriateTitle(billingObject);
    setError(false);
  }, [billingObject]);

  return (
    <>
      {content && (
        <Banner title={content.title} tone={content.tone}>
          <List>
            <List.Item>{content.desc}</List.Item>
          </List>
          <br></br>
          {error && (
            <WarningBanner
              messages={[
                "Some error occured while cancelling the subscription!",
                "Please try again or contact Support.",
              ]}
            />
          )}
          {!content.downgrade && (
            <Button
              primary
              onClick={redirectToBilling}
              disabled={!content.btn}
              loading={loading}
            >
              Upgrade for 9.99$/month
            </Button>
          )}
          {/* {content.downgrade && (
            <Button
              onClick={() => downgradePlan(content.subscriptionId)}
              disabled={!content.btn}
              loading={loading}
            >
              Downgrade
            </Button>
          )} */}
          {content.downgrade && (
            <DowngradeConfirmationModal
              disabled={!content.btn}
              loading={loading}
              downgradePlan={downgradePlan}
              subscriptionId={content.subscriptionId}
            />
          )}
        </Banner>
      )}
    </>
  );
}