import { checkSubscription } from "../../billing.js";
import { getAppInstalledMeta, creatAppInstalledMeta, checkOrdersCountFromInstalled } from "../Controller/getAppInstalledMeta.js"

export default async function validateSubscription(req, res, next) {
  const metaCheck = await getAppInstalledMeta(res.locals.shopify.session)
  if (!metaCheck) {
    await creatAppInstalledMeta(res.locals.shopify.session);
    next();
  } else {
    const data = await checkSubscription(res.locals.shopify.session);
    let allowed = false;
    if (data.developmentStore || data.subscriptionStatus) {
      allowed = true;
    } else {
      const ordersCount = await checkOrdersCountFromInstalled(metaCheck, res.locals.shopify.session);
      if (ordersCount.value <= 15) {
        allowed = true;
      } else {
        allowed = false;
      }
    }
    if (allowed) {
      next();
    } else {
      res.send({ error: "Subscription Needed" });
      return
    }
  }
}
