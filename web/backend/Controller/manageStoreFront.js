import axios from "axios";

const enableStoreFront = (namespace, key, token, shop) => {
  let data = JSON.stringify({
    query: `mutation {
  metafieldStorefrontVisibilityCreate(
    input: {
      namespace: "${namespace}"
      key: "${key}"
      ownerType: SHOP
    }
  ) {
    metafieldStorefrontVisibility {
      id
    }
    userErrors {
      field
      message
    }
  }
}`,
    variables: {}
  });

  let config = {
    method: 'post',
    maxBodyLength: Infinity,
    url: `https://${shop}/admin/api/2023-10/graphql.json`,
    headers: {
      'X-Shopify-Access-Token': `${token}`,
      'Content-Type': 'application/json'
    },
    data: data
  };

  axios.request(config)
    .then((response) => {
      console.log(response.data.data.metafieldStorefrontVisibilityCreate.userErrors[0])
    })
    .catch((error) => {
      console.log(error);
    });
}
export default enableStoreFront;