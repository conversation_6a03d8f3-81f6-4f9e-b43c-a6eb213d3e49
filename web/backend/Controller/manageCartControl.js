import axios from "axios";
import getShopID from "./getShopID.js";
import enableStoreFront from "./manageStoreFront.js";

const handleEditCartlMeta = async (req, res) => {
  let shop = req.res.locals.shopify.session.shop;
  let at = req.res.locals.shopify.session.accessToken;
  let shopID = await getShopID(shop, at)
  let metafieldSecret = { key: "Checkout_EditCart", namespace: "$app:EditCart" }
  const valueObj = JSON.stringify(req.body)
  axios
    .request({
      method: "post",
      url: `https://${shop}/admin/api/2023-10/graphql.json`,
      headers: {
        "x-shopify-access-token": at,
        "Content-Type": "application/json",
      },
      data: JSON.stringify({
        query: `mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
              metafieldsSet(metafields: $metafields) {
                metafields {
                  value
                }
                userErrors {
                  field
                  message
                }
              }
            }`,
        variables: {
          metafields: [
            {
              key: `${metafieldSecret.key}`,
              namespace: `${metafieldSecret.namespace}`,
              ownerId: shopID,
              type: "json",
              value: valueObj,
            },
          ],
        },
      }),
    })
    .then((response) => {
      enableStoreFront(metafieldSecret.namespace, metafieldSecret.key, at, shop);
      res.json("ok");
    })
    .catch((error) => {
      console.log(error);
    });
};

export default handleEditCartlMeta;
