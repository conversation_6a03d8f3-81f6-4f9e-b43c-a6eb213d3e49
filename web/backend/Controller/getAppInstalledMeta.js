import axios from "axios";
import getShopID from "./getShopID.js";

async function getAppInstalledMeta(req, res) {
    return new Promise((resolve, reject) => {
        let shop = req.shop;
        let at = req.accessToken;
        axios
            .request({
                method: "post",
                url: `https://${shop}/admin/api/2023-10/graphql.json`,
                headers: {
                    "x-shopify-access-token": at,
                    "Content-Type": "application/json",
                },
                data: JSON.stringify({
                    query: `query MyQuery {
          shop {
            AppMeta: metafield(key: "PayLocAppMeta", namespace: "$app:PayLocAppMeta") {
              value
            }
          }
        }`,
                }),
            })
            .then((response) => {
                let responseMetafield = response.data.data.shop.AppMeta
                resolve(responseMetafield);
            })
            .catch(function (error) {
                reject(error);
            });
    });
}
async function creatAppInstalledMeta(req, res) {
    return new Promise(async(resolve, reject) => {
        let shop = req.shop;
        let at = req.accessToken;
        let shopID = await getShopID(shop, at);
        const currentDateTime = new Date().toISOString();
        axios
        .request({
            method: "post",
            url: `https://${shop}/admin/api/2023-10/graphql.json`,
            headers: {
              "x-shopify-access-token": at,
              "Content-Type": "application/json",
            },
            data: JSON.stringify({
              query: `mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
                metafieldsSet(metafields: $metafields) {
                  metafields {
                    value
                  }
                  userErrors {
                    field
                    message
                  }
                }
              }`,
              variables: {
                metafields: [
                  {
                    key: "PayLocAppMeta",
                    namespace: "$app:PayLocAppMeta",
                    ownerId: shopID,
                    type: "json",
                    value: JSON.stringify(currentDateTime),
                  },
                ],
              },
            }),
          })
            .then((response) => {
                console.log(response.data.data.metafieldsSet.userErrors[0])
                resolve(response.data);
            })
            .catch(function (error) {
                reject(error);
            });
    });
}
async function checkOrdersCountFromInstalled(time,req, res) {
    return new Promise(async(resolve, reject) => {
        let shop = req.shop;
        let at = req.accessToken;
        console.log("8777",time.value)
        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: `https://${shop}/admin/api/2023-07/orders/count.json?created_at_min=${time.value}`,
            headers: { 
              'X-Shopify-Access-Token': `${at}`
            }
          };
          
          axios.request(config)
          .then((response) => {
            console.log(response.data)
            resolve(response.data);
        })
        .catch(function (error) {
            reject(error);
        });
    });
}

export {getAppInstalledMeta,creatAppInstalledMeta,checkOrdersCountFromInstalled} ;