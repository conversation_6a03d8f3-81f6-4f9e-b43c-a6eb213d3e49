import axios from "axios";

async function getShopMeta(req, res) {
  let shop = req.res.locals.shopify.session.shop;
  let at = req.res.locals.shopify.session.accessToken;
  axios
    .request({
      method: "post",
      url: `https://${shop}/admin/api/2023-10/graphql.json`,
      headers: {
        "X-Shopify-Access-Token": at,
        "Content-Type": "application/json",
      },
      data: JSON.stringify({
        query: `query MyQuery {
          shop {
            metafield(key: "Checkout_EditCart", namespace: "$app:EditCart") {
              value
            }
          }
        }`,
      }),
    })
    .then((response) => {
      const reponseMetafield = response.data.data.shop.metafield;
      if (reponseMetafield) {
        console.log(reponseMetafield);
        res.json(reponseMetafield);
      } else {
        res.json(null);
      }
    })
    .catch(function (error) {
      console.log(error);
      res.json(null);
    });
}

export default getShopMeta;
