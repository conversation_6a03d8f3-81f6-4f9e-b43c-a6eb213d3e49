import axios from "axios";
export default async function getShopID(shop, at) {
  return axios
    .request({
      method: "post",
      url: `https://${shop}/admin/api/2023-10/graphql.json`,
      headers: {
        "X-shopify-access-token": at,
        "Content-Type": "application/json",
      },
      data: JSON.stringify({
        query: `{
              shop {
                id
              }
            }`,
        variables: {},
      }),
    })
    .then((response) => {
      return response.data.data.shop.id;
    })
    .catch((error) => {
      console.log(error);
    });
}
