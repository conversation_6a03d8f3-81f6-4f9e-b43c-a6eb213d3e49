{"name": "shopify-app-template-node", "version": "1.0.0", "private": true, "license": "UNLICENSED", "scripts": {"debug": "node --inspect-brk index.js", "dev": "cross-env NODE_ENV=development nodemon index.js --ignore ./frontend", "serve": "cross-env NODE_ENV=production node index.js"}, "type": "module", "engines": {"node": ">=14.13.1"}, "dependencies": {"@shopify/shopify-app-express": "^2.1.3", "@shopify/shopify-app-session-storage-sqlite": "^1.2.3", "axios": "^1.5.0", "compression": "^1.7.4", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.3.1", "serve-static": "^1.14.1", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^2.0.15", "prettier": "^2.6.2", "pretty-quick": "^3.1.3"}}