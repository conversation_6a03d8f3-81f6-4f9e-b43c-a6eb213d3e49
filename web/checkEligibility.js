import axios from "axios";
export default async function checkStoreCompatibility(req, res) {
  let shop = req.res.locals.shopify.session.shop;
  let at = req.res.locals.shopify.session.accessToken;
  axios
    .request({
      method: "post",
      url: `https://${shop}/admin/api/2023-07/graphql.json`,
      headers: {
        "X-Shopify-Access-Token": `${at}`,
        "Content-Type": "application/json",
      },
      data: JSON.stringify({
        query: `{
          shop {
            name
            plan {
              shopifyPlus
              partnerDevelopment
            }
            checkoutApiSupported
          }
        }
        `,
        variables: {},
      }),
    })
    .then((response) => {
      if (
        (response.data.data.shop.plan.shopifyPlus &&
          response.data.data.shop.checkoutApiSupported) ||
        response.data.data.shop.plan.partnerDevelopment
      ) {
        res.send(true);
      } else {
        res.send(false);
      }
    })
    .catch((error) => {
      console.log(error);
    });
}
