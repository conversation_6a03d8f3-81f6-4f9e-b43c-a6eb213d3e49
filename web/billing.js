import { GraphqlQueryError } from "@shopify/shopify-api";
import { getAppInstalledMeta, creatAppInstalledMeta, checkOrdersCountFromInstalled } from "./backend/Controller/getAppInstalledMeta.js"
import shopify from "./shopify.js";

export async function createAppRecurringSubscription(session) {
  const client = new shopify.api.clients.Graphql({ session });
  try {
    const res = await client.query({
      data: {
        query: APP_SUBSCRIPTION_CREATE_MUTATION,
        variables: {
          name: "Super Duper Recurring Plan",
          returnUrl: `https://${session.shop}/admin/apps/checkout-discounts-offers`,
          test: true,
          lineItems: [
            {
              plan: {
                appRecurringPricingDetails: {
                  price: {
                    amount: 9.99,
                    currencyCode: "USD",
                  },
                  interval: "EVERY_30_DAYS",
                },
              },
            },
          ],
        },
      },
    });

    return {
      confirmationUrl: res.body.data.appSubscriptionCreate.confirmationUrl,
    };
  } catch (error) {
    if (error instanceof GraphqlQueryError) {
      console.log(error.message);
      throw new Error(
        `${error.message}\n${JSON.stringify(error.response, null, 2)}`
      );
    } else {
      throw error;
    }
  }
}

export async function cancelAppSubscription(session, id) {
  const client = new shopify.api.clients.Graphql({ session });
  try {
    const res = await client.query({
      data: {
        query: APP_SUBSCRIPTION_CANCEL_MUTATION,
        variables: { id: "gid://shopify/AppSubscription/" + id },
      },
    });
    if (res.body.data.appSubscriptionCancel.userErrors.length) {
      return {
        error: "Some Error ocurred while cancelling the subscription",
        errorData: res.body.data.appSubscriptionCancel.userErrors,
      };
    } else {
      return {
        status: "Cancelled",
      };
    }
  } catch (error) {
    if (error instanceof GraphqlQueryError) {
      console.log(error.message);
      throw new Error(
        `${error.message}\n${JSON.stringify(error.response, null, 2)}`
      );
    } else {
      throw error;
    }
  }
}

export async function checkSubscription(session) {
  let reponseObject = {
    developmentStore: false,
    shopifyPlus: false,
    checkoutApiSupported: false,
    subscriptionStatus: false,
    orderCount: 0
  };

  //for testing only
  // return {
  //   developmentStore: false,
  //   shopifyPlus: true,
  //   checkoutApiSupported: true,
  //   subscriptionStatus: false,
  // };

  try {
    const response = await shopify.api.rest.RecurringApplicationCharge.all({
      session,
    });

    const store_data = await checkStoreCompatibility(session);

    for (let index = 0; index < response.data.length; index++) {
      const element = response.data[index];
      if (element.status.toLowerCase() === "active") {
        reponseObject.subscriptionStatus = true;
        break;
      }
    }

    const shopData = store_data.body.data.shop;

    if (shopData) {
      reponseObject.checkoutApiSupported = shopData.checkoutApiSupported;
      reponseObject.developmentStore = shopData.plan.partnerDevelopment;
      reponseObject.shopifyPlus = shopData.plan.shopifyPlus;
      if (reponseObject.developmentStore || reponseObject.subscriptionStatus) {

      } else {
        const metaCheck = await getAppInstalledMeta(res.locals.shopify.session);
        if (!metaCheck) {
          await creatAppInstalledMeta(res.locals.shopify.session)
        } else {
          const ordersCount = await checkOrdersCountFromInstalled(metaCheck, res.locals.shopify.session);
          reponseObject.orderCount = ordersCount;
        }
      }
    } else {
      reponseObject.error = "Some error Occured";
    }
    return reponseObject;
  } catch (error) {
    console.log(error);
    reponseObject.error = error;
    return reponseObject;
  }
}

export async function checkStoreCompatibility(session) {
  const client = new shopify.api.clients.Graphql({ session });
  try {
    const response = await client.query({
      data: {
        query: SHOP_PLAN_QUERY,
        variables: {},
      },
    });
    return response;
  } catch (error) {
    if (error instanceof GraphqlQueryError) {
      console.log(error.message);
      throw new Error(
        `${error.message}\n${JSON.stringify(error.response, null, 2)}`
      );
    } else {
      throw error;
    }
  }
}

const APP_SUBSCRIPTION_CREATE_MUTATION = `mutation AppSubscriptionCreate($name: String!,$test: Boolean! $lineItems: [AppSubscriptionLineItemInput!]!, $returnUrl: URL!) {
  appSubscriptionCreate(name: $name, test: $test, returnUrl: $returnUrl, lineItems: $lineItems) {
    userErrors {
      field
      message
    }
    appSubscription {
      id
    }
    confirmationUrl
  }
}`;

const SHOP_PLAN_QUERY = `{
  shop {
    id
    checkoutApiSupported
    plan {
      displayName
      partnerDevelopment
      shopifyPlus
    }
  }
}
`;

const APP_SUBSCRIPTION_CANCEL_MUTATION = `mutation AppSubscriptionCancel($id: ID!) {
  appSubscriptionCancel(id: $id) {
    userErrors {
      field
      message
    }
    appSubscription {
      id
      status
    }
  }
}
`;