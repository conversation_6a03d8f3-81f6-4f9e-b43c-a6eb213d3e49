export const formatDataTwo = (variantDetails, selected, variants) => {

    let filterApply = variantDetails.map((x) => x.values.map((z) =>
    (z + ' / ' + (selected.filter((y) => y.name !== x.name).map((e) => e.value))).split('/').join(',')))

    filterApply = filterApply.map(subArray => subArray.map(str => str.replace(/ /g, '')))
    filterApply = filterApply.map(subArray => subArray.map(str => str.replace(/,/g, ' / ')));
 
    const resultArray = filterApply.map(subArray => subArray.map(str => str.split(' / ')));

   

    let selectedFinal = resultArray.map((subArray, i) => {
        const combinedItems = subArray.map(item => (i === 0 ? item.join(' / ') : (item.reverse()).join(' / ')));
        return combinedItems;
    })
    
    return selectedFinal.map((n) => n.map((d) => variants.edges.some((z) => d === z.node.title)))
}

