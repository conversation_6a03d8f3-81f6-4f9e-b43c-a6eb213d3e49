import {
  Pressable,
  reactExtension,
  Popover,
  View,
  Text,
  Stepper,
  useTarget,
  useApplyCartLinesChange,
  Divider,
  BlockSpacer,
  Button,
  Select,
  Disclosure,
  InlineLayout,
  Icon,
  BlockStack,
  Spinner,
  Checkbox,
  useApi,
} from "@shopify/ui-extensions-react/checkout";
import { Fragment } from "react";
import { useEffect } from "react";
import { useState } from "react";
import { formatDataThree } from "./threeVariant";
import { formatDataTwo } from "./twoVariant";
import { formatDataOne } from "./oneVariant";

export default reactExtension(
  "purchase.checkout.cart-line-item.render-after",
  () => <Extension />
);

function Extension() {
  const [isLoading, setIsLoading] = useState(false);
  const [isTag, setIsTag] = useState({ status: false, isLoading: true });
  const [variantDetails, setVariantDetials] = useState(null);
  const [variantSellingPlan, setVariantSellingPlan] = useState([]);
  const [autoDisabled, setAutoDisabled] = useState([]);
  const [openId, setOpenId] = useState(false);
  const [sellingPlanList, setSellingPlanList] = useState([]);
  const [selectedPlan, setSelectPlan] = useState({});
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [getMetafield, setGetMetafield] = useState({});
  const [selectedVariant, setSelectedVariant] = useState({
    name: null,
    index: null,
    value: { index: null, value: null },
  });

  const applyCartLinesChange = useApplyCartLinesChange();
  const data = useTarget();
  const { query } = useApi();

  // ### Add line item qty handler ### //
  const qtyHandler = async (qty) => {
    setIsLoading(true);
    await applyCartLinesChange({
      type: "updateCartLine",
      id: `${data.id}`,
      quantity: qty,
    });
    setIsLoading(false);
  };

  // ### Delete Line Item ### //
  const deleteLine = async () => {
    setIsLoading(true);
    await applyCartLinesChange({
      type: "updateCartLine",
      id: `${data?.id}`,
      quantity: 0,
    });
    setIsLoading(false);
  };

  const selected = variantDetails
    ? variantDetails?.product?.options?.map((x) =>
        x.values.findIndex((y) =>
          data?.merchandise?.selectedOptions?.some((n) => n.value === y)
        )
      )
    : "loading...";
  selected[selectedVariant?.index] = selectedVariant?.value?.index;
  let finalSelected = variantDetails
    ? variantDetails?.product?.options?.map((x, index) =>
        x.values.filter((y, i) => i === selected[index])
      )
    : [];

  // #### __Update__Varaint__Handler__ #### //
  const changeHandler = async () => {
    setIsLoading(true);
    await applyCartLinesChange({
      type: "updateCartLine",
      id: data?.id,
      merchandiseId: `${
        variantDetails?.product?.variants?.edges?.filter(
          (x) => x?.node?.title === finalSelected.flat(Infinity).join(" / ")
        )[0]?.node?.id
      }`,
      quantity: data?.quantity,
    });
    setIsLoading(false);
  };

  // ### Change Subscribe Handler (checkbox) ### //
  const changeSubscribeHandler = async (value) => {
    setIsLoading(true);
    if (value) {
      setIsLoading(false);
      setIsSubscribed(true);
    } else {
      await applyCartLinesChange({
        type: "updateCartLine",
        id: `${data?.id}`,
        sellingPlanId: null,
      });
      setIsSubscribed(false);
      setIsLoading(false);
      setSelectPlan({});
    }
  };

  // ## Update Variant ##
  useEffect(() => {
    if (selectedVariant.name) {
      changeHandler();
    }
  }, [selectedVariant]);

  // ### Auto Disabled Varaint Functionality here ### //
  useEffect(() => {
    if (
      variantDetails?.product?.options &&
      data?.merchandise?.selectedOptions &&
      variantDetails?.product?.variants
    ) {
      if (variantDetails?.product?.options?.length === 3) {
        setAutoDisabled([]);
        setAutoDisabled(
          formatDataThree(
            variantDetails?.product?.options,
            data?.merchandise?.selectedOptions,
            variantDetails?.product?.variants
          )
        );
      } else if (variantDetails?.product?.options?.length === 2) {
        setAutoDisabled([]);
        setAutoDisabled(
          formatDataTwo(
            variantDetails?.product?.options,
            data?.merchandise?.selectedOptions,
            variantDetails?.product?.variants
          )
        );
      } else {
        setAutoDisabled([]);
        setAutoDisabled([
          formatDataOne(
            variantDetails?.product?.options,
            variantDetails?.product?.variants
          ),
        ]);
      }
    }
  }, [variantDetails, data]);

  // ### Add/Remove Selling Plans ### //
  const setPlanHandler = async (value) => {
    setIsLoading(true);
    await applyCartLinesChange({
      type: "updateCartLine",
      id: `${data?.id}`,
      sellingPlanId: `${sellingPlanList[value].id}`,
    });
    setIsLoading(false);
    setIsSubscribed(true);
  };

  // ### Get All Selling Plans & Selected Plan  ### //
  const getSellingPlans = async () => {
    setIsSubscribed(false);
    setIsLoading(true);
    const selectedPlanFilter =
      variantSellingPlan.length > 0 &&
      variantSellingPlan.filter(
        (x) => x.id === data?.merchandise?.sellingPlan?.id
      );
    if (selectedPlanFilter.length > 0) {
      setIsSubscribed(true);
      setIsLoading(false);
      setSelectPlan(selectedPlanFilter[0]);
    } else {
      setIsSubscribed(false);
      setIsLoading(false);
      setSelectPlan({});
    }
    variantSellingPlan.length > 0
      ? setSellingPlanList([
          { name: "Select one option", id: "" },
          ...variantSellingPlan,
        ])
      : setSellingPlanList([]);
  };

  useEffect(() => {
      getSellingPlans();
  }, [data, variantSellingPlan]);

  // ### Get App Metafield ### //
  useEffect(() => {
    query(
      `query MyQuery {
        shop {
          metafield(key: "Checkout_EditCart", namespace: "$app:EditCart") {
            value
          }
        }
      }`
    )
      .then(({ data, errors }) => {
        errors && console.log(errors);
        if (!errors) {
          setGetMetafield(JSON.parse(data?.shop?.metafield?.value));
        }
      })
      .catch(console.error);
  }, [query]);

  // ### Tag storefront query __Get Tag__ ### //
  useEffect(() => {
    setIsTag({ status: false, isLoading: true });
    query(
      `{ 
        product(id: "gid://shopify/Product/${
          data?.merchandise?.product?.id.split("/")[
            data?.merchandise?.product?.id.split("/").length - 1
          ]
        }") {
          tags
        }
      }`
    )
      .then(({ data, errors }) => {
        errors && console.log(errors);
        if (!errors) {
          setIsTag({ status: false, isLoading: false });
          if (data?.product?.tags.includes(getMetafield?.PT)) {
            setIsTag({ status: true, isLoading: false });
          }
        }
      })
      .catch((err) => {
        setIsTag({ status: false, isLoading: false });
        console.log(err);
      });
  }, [query, getMetafield]);

  // ### Get product Variant ### //
  useEffect(() => {
    query(
      `{
        product(id: "gid://shopify/Product/${
          data?.merchandise?.product?.id.split("/")[
            data?.merchandise?.product?.id.split("/").length - 1
          ]
        }") {
          variants(first: 100) {
            edges {
              node {
                id
                title
              }
            }
          }
          options(first: 100) {
            name
            values
          }
        }
      }`
    )
      .then(({ data, errors }) => {
        errors && console.log(errors);
        if (!errors) {
          setVariantDetials(data);
        }
      })
      .catch(console.error);
  }, [query]);

  // ### get variant selling plan list ### //
  useEffect(() => {
    query(
      `query MyQuery {
      nodes(ids: "${data?.merchandise?.id}") {
        ... on ProductVariant {
          id
          sellingPlanAllocations(first: 100) {
            nodes {
              sellingPlan {
                id
                name
                description
              }
            }
          }
        }
      }
    }
      `
    )
      .then(({ data, errors }) => {
        errors && console.log(errors);
        if (!errors) {
          setVariantSellingPlan(
            data?.nodes?.length > 0 &&
              data?.nodes[0]?.sellingPlanAllocations?.nodes?.map((node) => {
                const { id, name } = node.sellingPlan;
                return { id, name };
              })
          );
        }
      })
      .catch(console.error);
  }, [query]);

  return !isTag.status && isTag.isLoading === false ? (
    <Pressable
      disabled={isLoading}
      onPress={() => {
        setOpenId(false);
      }}
      overlay={
        <Popover position={"blockEnd"}>
          <View minInlineSize={300} padding="tight">
            <BlockStack spacing="none" border={"base"} borderRadius={"base"}>
              <Disclosure
                onToggle={() => {
                  if (openId) {
                    setOpenId(false);
                  } else {
                    setOpenId(true);
                  }
                }}
              >
                <Pressable
                  toggles="one"
                  padding={openId ? "tight" : "base"}
                  disabled={isLoading}
                >
                  <InlineLayout
                    blockAlignment="center"
                    spacing="none"
                    columns={["auto", "fill", "auto"]}
                  >
                    <Text appearance="subdued">Select Variant</Text>
                    <View></View>
                    <Icon
                      source={openId ? "chevronUp" : "chevronDown"}
                      appearance="subdued"
                    />
                  </InlineLayout>

                  {/* ## Varaints Select ## */}
                </Pressable>
                {variantDetails && autoDisabled.length > 0 ? (
                  <View id="one" padding={"tight"}>
                    {variantDetails?.product?.options?.map((x, i) => {
                      return (
                        <Fragment key={i}>
                          <Select
                            disabled={isLoading}
                            label={x?.name}
                            value={selected ? selected[i] : "0"}
                            onChange={(value) => {
                              setSelectedVariant({
                                name: x?.name,
                                index: i,
                                value: {
                                  index: Number(value),
                                  value: x?.values[value],
                                },
                              });
                            }}
                            options={x?.values?.map((y, index) => {
                              return {
                                label: y,
                                value: index,
                                disabled: !autoDisabled[i][index],
                              };
                            })}
                          />
                          <BlockSpacer spacing="extraTight" />
                        </Fragment>
                      );
                    })}
                  </View>
                ) : null}
              </Disclosure>
            </BlockStack>
            {/* ### Subscribtion Code ### */}
            {sellingPlanList.length > 0 && (
              <>
                <BlockSpacer spacing="tight" />

                <Checkbox
                  id="checkbox2"
                  name="checkbox2"
                  checked={isSubscribed}
                  disabled={isLoading}
                  onChange={changeSubscribeHandler}
                >
                  Subscribe and Save
                </Checkbox>
              </>
            )}
            {isSubscribed && sellingPlanList.length > 0 && (
              <>
                <BlockSpacer spacing="tight" />
                {/* ## Subscription ## */}
                <Select
                  label="Deliver every"
                  disabled={isLoading}
                  value={
                    sellingPlanList.findIndex(
                      (obj) => obj.name === selectedPlan?.name
                    ) === -1
                      ? 0
                      : sellingPlanList.findIndex(
                          (obj) => obj.name === selectedPlan?.name
                        )
                  }
                  onChange={setPlanHandler}
                  options={sellingPlanList?.map((x, i) => {
                    return {
                      label: x.name,
                      value: i,
                      disabled: i === 0,
                    };
                  })}
                />
              </>
            )}
            <BlockSpacer spacing="tight" />

            {/* ### Add/Remove QTY && Remove ### */}
            <Stepper
              disabled={isLoading}
              label="Quantity"
              value={data?.quantity}
              onChange={(number) => {
                qtyHandler(number);
              }}
            />

            <BlockSpacer spacing="tight" />
            <Divider />
            <View inlineAlignment={"center"} blockAlignment={"center"}>
              <Button
                kind="plain"
                appearance="critical"
                onPress={deleteLine}
                disabled={isLoading}
              >
                Remove
              </Button>
            </View>
          </View>
        </Popover>
      }
    >
      {data?.merchandise?.id ===
      `gid://shopify/ProductVariant/${getMetafield?.PV}` ? (
        <Button
          kind="plain"
          appearance="critical"
          onPress={deleteLine}
          disabled={isLoading}
        >
          Remove
        </Button>
      ) : (
        <Text appearance={"accent"} size="small">
          Modify
        </Text>
      )}
    </Pressable>
  ) : (
    <>{!isTag.status && <Spinner size="small" />}</>
  );
}