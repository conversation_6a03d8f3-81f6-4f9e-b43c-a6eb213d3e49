import {
  reactExtension,
  View,
  TextBlock,
  useCartLines,
  Divider,
  BlockSpacer,
  InlineLayout,
  Checkbox,
  Spinner,
  TextField,
  useApplyCartLinesChange,
  useNote,
  useApplyNoteChange,
  useApi,
  Link,
  Button,
  useShop,
} from "@shopify/ui-extensions-react/checkout";
import { useState } from "react";
import { useEffect } from "react";

export default reactExtension(
  "purchase.checkout.cart-line-list.render-after",
  () => <Extension />
);

function Extension() {
  const [isLoading, setIsLoading] = useState(false);
  const [cartNote, setCartNote] = useState("");
  const [getMetafield, setGetMetafield] = useState({});
  const cartItem = useCartLines();
  const changeNote = useApplyNoteChange();
  const notes = useNote();
  const applyCartLinesChange = useApplyCartLinesChange();

  const { query } = useApi();
  const shopData = useShop();
 
  // ### Update Cart Item == Gift Box ### //
  const updateHandler = async () => {
    setIsLoading(true);
    // to add line item /// gift wrap
    await applyCartLinesChange({
      type: "addCartLine",
      merchandiseId: `gid://shopify/ProductVariant/${getMetafield?.PV}`,
      quantity: 1,
    });
    setIsLoading(false);
  };

  // ### Remove Cart Item == Gift Box ### //
  const removeHandler = async () => {
    setIsLoading(true);
    // to remove line item /// gift wrap
    await applyCartLinesChange({
      type: "updateCartLine",
      id: `${
        cartItem?.find(
          (x) =>
            x?.merchandise?.id ===
            `gid://shopify/ProductVariant/${getMetafield?.PV}`
        )?.id
      }`,
      merchandiseId: `gid://shopify/ProductVariant/${getMetafield?.PV}`,
      quantity: 0,
    });
    await changeNote({
      type: "updateNote",
      note: null,
    });
    setCartNote("");
    setIsLoading(false);
  };

  // ### Checkbox ## addGiftHandler == Gift Box ### //
  const addGiftHandler = (value) => {
    if (value) {
      updateHandler();
    } else {
      removeHandler();
    }
  };

  // ### Update Gift Message ### //
  const updateNote = async (value) => {
    await changeNote({
      type: "updateNote",
      note: `${value}`,
    });
    setCartNote(value);
  };

// ### Get Metafield for conditions ### //
  useEffect(() => {
    query(
      `query MyQuery {
          shop {
            metafield(key: "Checkout_EditCart", namespace: "$app:EditCart") {
              value
            }
          }
        }`
    )
      .then(({ data, errors }) =>
        setGetMetafield(JSON.parse(data?.shop?.metafield?.value))
      )
      .catch(console.error);
  }, [query]);

  // ### Set Gift Message to Cart Note ### //
  useEffect(() => {
    setCartNote(notes);
  }, [notes]);

  return (
    <>
      {/* ~ When Cart is Empty ~ */}
      <View>
        {cartItem.length === 0 && (
          <>
            <TextBlock
              inlineAlignment="center"
              blockAlignment="center"
              size="extraLarge"
            >
              Your cart is empty
            </TextBlock>
            <View
              padding="tight"
              blockAlignment={"center"}
              inlineAlignment={"center"}
            >
              {/* Add link url to go for home/  */}
              <Link to={shopData?.storefrontUrl}>
                <Button>Continue Shopping</Button>
              </Link>
            </View>
          </>
        )}
      </View>

      {/* ~ Gift Wrap Logic ~ */}
      {getMetafield?.GW === true && (
        <View>
          {cartItem.length !== 0 && (
            <>
              <Divider />
              <BlockSpacer spacing="base" />
              {getMetafield?.GW && (
                <InlineLayout
                  columns={["auto", "fill"]}
                  blockAlignment={"center"}
                >
                  <View
                    padding={"none"}
                    blockAlignment={"center"}
                    inlineAlignment={isLoading ? "center" : "start"}
                  >
                    <Checkbox
                      id="checkbox"
                      name="checkbox"
                      checked={cartItem?.some(
                        (x) =>
                          x?.merchandise?.id ===
                          `gid://shopify/ProductVariant/${getMetafield?.PV}`
                      )}
                      onChange={addGiftHandler}
                      disabled={isLoading}
                    >
                      Add a gift box
                    </Checkbox>
                  </View>
                  <View></View>
                </InlineLayout>
              )}
            </>
          )}
        </View>
      )}
      {/* Gift Message Logic */}
      {getMetafield?.GM === true && (
        <View>
          {cartItem.length !== 0 && (
            <>
              {getMetafield?.GW !== true && (
                <>
                  <Divider />
                  <BlockSpacer spacing="base" />
                </>
              )}
              <>
                {isLoading && getMetafield?.GW !== true ? (
                  <>
                    <InlineLayout
                      columns={["fill", "auto", "fill"]}
                      blockAlignment={"center"}
                    >
                      <View></View>
                      <View>
                        <Spinner size="small" />
                      </View>
                      <View></View>
                    </InlineLayout>
                  </>
                ) : (
                  <>
                    {getMetafield?.GM && (
                      <>
                        <BlockSpacer spacing="tight" />
                        <TextField
                          disabled={isLoading}
                          label="Gift messaging (optional)"
                          value={notes || cartNote}
                          multiline={true}
                          onChange={updateNote}
                        />
                        <BlockSpacer spacing="loose" />
                      </>
                    )}
                  </>
                )}
              </>
            </>
          )}
        </View>
      )}
    </>
  );
}