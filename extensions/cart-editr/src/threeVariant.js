export const formatDataThree = (variantDetails, selected, variants) => {

    let filterApply = variantDetails?.map((x) => x.values.map((z) =>
    (z + ' / ' + (selected.filter((y) => y.name !== x.name).map((e) => e.value))).split('/').join(',')))

    filterApply = filterApply.map(subArray => subArray.map(str => str.replace(/ /g, '')))
    filterApply = filterApply.map(subArray => subArray.map(str => str.replace(/,/g, ' / ')));
 
    const resultArray = filterApply.map(subArray => subArray.map(str => str.split(' / ')));

    let arr2 = resultArray
    for (let i = 0; i < arr2.length - 1; i++) {
        let temp = arr2[1][i][0]
        arr2[1][i][0] = arr2[1][i][1]
        arr2[1][i][1] = temp
    }

    let arr3 = arr2
    for (let i = 0; i < arr2.length - 1; i++) {
        let temp = arr2[2][i][0]
        arr2[2][i][0] = arr2[2][i][1]
        arr2[2][i][1] = temp
    }

    let arr4 = arr3
    for (let i = 0; i < arr2.length - 1; i++) {
        let temp = arr2[2][i][1]
        arr2[2][i][1] = arr2[2][i][0]
        arr2[2][i][1] = temp
    }

    let arr5 = arr4
    for (let i = 0; i < arr2.length - 1; i++) {
        let temp = arr2[2][i][1]
        arr2[2][i][1] = arr2[2][i][2]
        arr2[2][i][2] = temp
    }

    let selectedFinal = arr5.map(innerArray => {
    const formattedInnerArray = innerArray.map(item => item.join(' / '));
    return formattedInnerArray;
  })

    return selectedFinal?.map((n) => n.map((d) => variants.edges.some((z) => d === z.node.title)))
}

