# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

name = "<PERSON>t Editr"
client_id = "e954cf6cb6bf95e0be2e2f8b4a8b9a40"
application_url = "https://0mimav5nb4.execute-api.ap-south-1.amazonaws.com"
embedded = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products"
use_legacy_install_flow = true

[auth]
redirect_urls = [
  "https://0mimav5nb4.execute-api.ap-south-1.amazonaws.com/auth/callback",
  "https://0mimav5nb4.execute-api.ap-south-1.amazonaws.com/api/auth/callback"
]

[webhooks]
api_version = "2023-10"

  [webhooks.privacy_compliance]
  customer_deletion_url = "https://0mimav5nb4.execute-api.ap-south-1.amazonaws.com/customer/erasure"
  customer_data_request_url = "https://0mimav5nb4.execute-api.ap-south-1.amazonaws.com/customer/request"
  shop_deletion_url = "https://0mimav5nb4.execute-api.ap-south-1.amazonaws.com/shop/erasure"

[pos]
embedded = false

[build]
automatically_update_urls_on_dev = false
dev_store_url = "payperks-demo.myshopify.com"
